package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextGray999

/**
 * 应用统一的弹窗组件
 *
 * @param isShown 是否显示弹窗
 * @param modifier 弹窗修饰符
 * @param backgroundColor 弹窗背景颜色
 * @param cancelText 取消按钮文字, null 时不显示取消按钮
 * @param confirmText 确认按钮文字, null 时不显示确认按钮
 * @param shape 弹窗形状
 * @param containerPaddingValues 弹窗内容 padding
 * @param onDismissRequest 当用户试图解散对话框时执行
 * @param content 弹窗内容
 *
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBottomSheetDialog(
    isShown: Boolean,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colorScheme.surface,
    cancelText: String? = stringResource(R.string.cancel),
    confirmText: String? = stringResource(R.string.confirm),
    shape: Shape = DEFAULT_DIALOG_SHAPE,
    containerPaddingValues: PaddingValues = PaddingValues(0.dp),
    onDismissRequest: () -> Unit,
    onConfirmRequest: () -> Unit,
    dragHandle: @Composable () -> Unit = { BottomSheetDefaults.DragHandle() },
    contentWindowInsets: @Composable () -> WindowInsets = { WindowInsets.ime },
    content: @Composable () -> Unit
) {
    if (!isShown) return

    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        shape = shape,
        dragHandle = dragHandle,
        contentWindowInsets = contentWindowInsets,
        properties = ModalBottomSheetDefaults.properties,
    ) {
        Box(
            modifier = modifier
                .background(backgroundColor, shape)
                .padding(containerPaddingValues)
                .padding(bottom = WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding())
        ) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    if (cancelText != null) {
                        BodyText(
                            text = cancelText,
                            size = BodySize.Small,
                            modifier = Modifier.clickable(onClick = onDismissRequest),
                            color = TextGray999
                        )
                    }

                    if (confirmText != null) {
                        BodyText(
                            text = confirmText,
                            size = BodySize.Small,
                            modifier = Modifier.clickable(onClick = {
                                onConfirmRequest()
                                onDismissRequest()
                            }),
                            color = PurplePrimary
                        )
                    }
                }

                content()
            }
        }
    }
}
private val DEFAULT_DIALOG_CORNER_RADIUS = 24.dp
private val DEFAULT_DIALOG_SHAPE = RoundedCornerShape(topStart = DEFAULT_DIALOG_CORNER_RADIUS, topEnd = DEFAULT_DIALOG_CORNER_RADIUS)

@Preview
@Composable
private fun AppBottomSheetDialogPreview() {
    AppTheme {
        AppBottomSheetDialog(
            isShown = true,
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            containerPaddingValues = PaddingValues(horizontal = 20.dp, vertical = 15.dp),
            onDismissRequest = {},
            onConfirmRequest = {},
        ) {
            BodyText(text = "Hello World")
        }
    }
}