package com.flutterup.app.design.theme

import androidx.annotation.VisibleForTesting
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * Light default theme color scheme
 */
@VisibleForTesting
val DefaultColorScheme = lightColorScheme(
    primary = Color.White,
    onPrimary = Color.White,
    primaryContainer = PurpleSecondary,
    onPrimaryContainer = Color.White,
    secondary = Color.White,
    onSecondary = Color.White,
    secondaryContainer = Color.White,
    onSecondaryContainer = Color.White,
    tertiary = Color.White,
    onTertiary = Color.White,
    tertiaryContainer = PurpleTertiaryContainer,
    onTertiaryContainer = Color.White,
    error = ErrorPrimary,
    onError = ErrorPrimary,
    errorContainer = Color.White,
    onErrorContainer = Color.White,
    background = BackgroundPrimary,
    onBackground = Color.White,
    surface = PinkPrimary,
    onSurface = Color.White,
    surfaceVariant = Color.White,
    onSurfaceVariant = Color.White,
    inverseSurface = Color.White,
    inverseOnSurface = Color.White,
    outline = Color.White,
)

val ColorScheme.DefaultBackgroundTheme get() = BackgroundTheme(
    color = surface,
    tonalElevation = 2.dp,
)

/**
 * Now in Android theme.
 */
@Composable
fun AppTheme(
    colorScheme: ColorScheme = DefaultColorScheme,
    backgroundTheme: BackgroundTheme = colorScheme.DefaultBackgroundTheme,
    content: @Composable () -> Unit,
) {
    // Composition locals
    CompositionLocalProvider(
        LocalBackgroundTheme provides backgroundTheme
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = FlutterUpTypography,
            content = content,
        )
    }
}

