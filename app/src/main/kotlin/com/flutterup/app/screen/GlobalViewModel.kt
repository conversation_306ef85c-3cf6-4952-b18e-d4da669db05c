package com.flutterup.app.screen

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import com.flutterup.app.model.UploadResponse
import com.flutterup.app.network.GlobalApiService
import com.flutterup.base.BaseRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.utils.DateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import kotlin.io.readBytes

@HiltViewModel
class GlobalViewModel @Inject constructor(
    private val repository: GlobalRepository
) : BaseRepositoryViewModel(repository) {


    fun uploadFileFromUri(
        uri: Uri,
        onSuccess: (String) -> Unit,
        onStart: () -> Unit = {},
        onComplete: () -> Unit = {},
    ) {
        onStart()
        scope.launch {
            val result = repository.uploadUri(uri)
            if (result != null) {
                onSuccess(result)
            }
        }.invokeOnCompletion {
            onComplete()
        }
    }
}

class GlobalRepository @Inject constructor(
    private val globalApiService: GlobalApiService,
    @ApplicationContext private val context: Context,
) : BaseRepository() {

    suspend fun uploadUri(uri: Uri): String? {
        val contentResolver = context.contentResolver
        val inputStream: InputStream? = contentResolver.openInputStream(uri)
        val mimeType: String? = contentResolver.getType(uri)
        val fileName = getFileNameByMineType(uri, contentResolver, mimeType)

        if (inputStream != null) {
            try {
                // 将 InputStream 转换为 RequestBody
                // 注意：readBytes() 会将整个文件读入内存，对大文件不友好
                val requestBody = inputStream.readBytes().toRequestBody(mimeType?.toMediaTypeOrNull())
                inputStream.close() // 关闭流

                val urlResponse = getUploadUrl(fileName) ?: return null
                globalApiService.uploadFile(urlResponse.uploadUrl, requestBody)
                return urlResponse.cdnUrl
            } finally {
                try {
                    inputStream.close()
                } catch (_: IOException) { // ignore this
                }
            }
        }
        return null
    }

    private suspend fun getUploadUrl(fileName: String): UploadResponse? {
        val result = globalApiService.getUploadUrl(fileName)
        return result.data
    }

    private fun getFileNameByMineType(uri: Uri, contentResolver: ContentResolver, mimeType: String?): String {
        val postfix = mimeTypeToPostfix(mimeType)

        // 获取文件名
        var filename = getFileName(uri, contentResolver)
        if (filename.isNullOrEmpty()) {
            val sb = StringBuilder(DateUtils.formatTimestamp(System.currentTimeMillis(), DateUtils.defaultFileDateFormat))
            if (postfix != null) {
                sb.append(".")
                sb.append(postfix)
            }
            filename = sb.toString()
        }
        return filename
    }

    private fun mimeTypeToPostfix(mimeType: String?): String? {
        if (mimeType == null) return null

        return when {
            mimeType.startsWith("image/") -> ".png"
            mimeType.startsWith("video/") -> ".mp4"
            else -> null
        }
    }

    private fun getFileName(uri: Uri, contentResolver: ContentResolver): String? {
        var result: String? = null
        if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    if (nameIndex != -1) {
                        result = cursor.getString(nameIndex)
                    }
                }
            }
        }
        if (result == null) {
            result = uri.path
            val cut = result?.lastIndexOf('/')
            if (cut != -1 && cut != null) {
                result = result.substring(cut + 1)
            }
        }
        return result
    }
}