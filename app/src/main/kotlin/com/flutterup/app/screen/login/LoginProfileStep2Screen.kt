@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.noRippleClickable
import com.flutterup.app.design.text.LabelSize
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PinkSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.screen.GlobalViewModel
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.app.screen.login.vm.LoginProfileStep2ViewModel
import com.flutterup.app.utils.MediaSizeCheckUtils


@Composable
fun LoginProfileStep2Screen() {
    val navController = LocalNavController.current
    val context = LocalContext.current
    val viewModel: LoginProfileStep2ViewModel = hiltViewModel()
    val globalViewModel: GlobalViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    var occupiedIndex by remember { mutableIntStateOf(-1) }

    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
        if (uri == null || occupiedIndex == -1) return@rememberLauncherForActivityResult

        if (MediaSizeCheckUtils.sizeCheck(context, uri)) { //检查通过
            globalViewModel.uploadFileFromUri(
                uri = uri,
                onStart = {
                    viewModel.startMediaLoading(occupiedIndex)
                },
                onSuccess = {
                    viewModel.addMedia(occupiedIndex, it)
                },
                onComplete = {
                    viewModel.endMediaLoading(occupiedIndex)
                    occupiedIndex = -1
                }
            )
        }
    }

    LoginProfileStep2Content(
        uiState = uiState,
        onSkipClick = { navController.navigate(HomeBaseRoute) },
        onItemClick = { index ->
            val item = uiState.mediaStatuses.getOrNull(index) ?: return@LoginProfileStep2Content

            if (item is LoginProfileStep2UIState.MediaStatus.Idle) { //无照片状态
                occupiedIndex = uiState.firstIdleIndex //更新index
                launcher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            } else if (item is LoginProfileStep2UIState.MediaStatus.Success) { //已经有照片
                //TODO 跳转到图片编辑页
            }
        },
    )
}

@Composable
private fun LoginProfileStep2Content(
    uiState: LoginProfileStep2UIState,
    onBackClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
    onItemClick: (index: Int) -> Unit = {},
) {
    AppScaffold(
        title = { },
        canGoBack = false, //不允许返回
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        rightNavigationContent = {
            LoginProfileStep2Skip(
                modifier = Modifier.width(50.dp).height(25.dp),
                onClick = onSkipClick
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            Column (
                modifier = Modifier.fillMaxSize()
            ) {
                Spacer(
                    modifier = Modifier.padding(top = it.calculateTopPadding() + 10.dp)
                )

                LoginProfileStep2Photo(
                    medias = uiState.mediaStatuses,
                    modifier = Modifier.padding(horizontal = 16.dp),
                    onClick = onItemClick
                )
            }
        }
    }
}

@Composable
private fun LoginProfileStep2Skip(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.fillMaxSize().wrapContentHeight(),
        shape = RoundedCornerShape(30.dp),
        border = BorderStroke(1.dp, PurplePrimary),
        contentPadding = PaddingValues(top = 0.dp, bottom = 2.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = PurplePrimary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = PurplePrimary,
        )
    ) {
        LabelText(
            text = stringResource(R.string.skip),
            size = LabelSize.Large,
            color = PurplePrimary
        )
    }
}

@Composable
private fun LoginProfileStep2Photo(
    modifier: Modifier = Modifier,
    medias: List<LoginProfileStep2UIState.MediaStatus>,
    onClick: (index: Int) -> Unit
) {
    fun onItemClick(index: Int): () -> Unit = {
        if (medias.none { it is LoginProfileStep2UIState.MediaStatus.Loading }) {
            onClick(index)
        }
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        items(
            count = 5,
            span = { if (it == 0) GridItemSpan(2) else GridItemSpan(1) }
        ) { index ->
            when(index) {
                1 -> Column(
                    verticalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    LoginProfileStep2PhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .noRippleClickable(onClick = onItemClick(1)),
                        mediaStatus = medias.getOrNull(1),
                        isMainPhoto = false
                    )


                    LoginProfileStep2PhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .noRippleClickable(onClick = onItemClick(2)),
                        mediaStatus = medias.getOrNull(2),
                        isMainPhoto = false
                    )
                }

                else -> {
                    val realIndex = if (index == 0) 0 else index + 1

                    LoginProfileStep2PhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .padding(if (index == 0) 1.dp else 0.dp)
                            .noRippleClickable(onClick = onItemClick(realIndex)),
                        mediaStatus = medias.getOrNull(realIndex),
                        isMainPhoto = index == 0
                    )
                }
            }
        }
    }
}

@Composable
private fun LoginProfileStep2PhotoItem(
    modifier: Modifier = Modifier,
    mediaStatus: LoginProfileStep2UIState.MediaStatus?,
    isMainPhoto: Boolean = false,
) {
    val stroke = Stroke(
        width =  with(LocalDensity.current) { 2.dp.toPx() },
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
    )

    val shape = RoundedCornerShape(20.dp)

    Box(
        modifier = modifier
            .background(
                color = PurplePrimary.copy(alpha = 0.21f),
                shape = shape
            )
    ) {
        Box(
            modifier = modifier
                .background(
                    brush = itemLinearGradient,
                    shape = shape
                )
                .then(
                    if (isMainPhoto) {
                        Modifier.drawBehind {
                            drawRoundRect(
                                color = PurplePrimary,
                                style = stroke,
                                cornerRadius = CornerRadius(20.dp.toPx())
                            )
                        }
                    } else {
                        Modifier
                    }
                )
        ) {
            Spacer(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        brush = itemVerticalGradient,
                        shape = shape
                    )
            )

            when (mediaStatus) {
                is LoginProfileStep2UIState.MediaStatus.Loading -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(35.dp)
                            .align(Alignment.Center),
                        color = Color.White,
                    )
                }

                is LoginProfileStep2UIState.MediaStatus.Success -> {
                    AsyncImage(
                        model = mediaStatus.url,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.matchParentSize().clip(shape)
                    )
                }
                else -> {
                    if (isMainPhoto) {
                        Icon(
                            painter = painterResource(R.drawable.ic_upload_pic),
                            contentDescription = null,
                            tint = PurplePrimary,
                            modifier = Modifier.size(50.dp)
                                .align(Alignment.Center)
                        )
                    } else {
                        Icon(
                            painter = painterResource(R.drawable.ic_logo),
                            contentDescription = null,
                            tint = Color.White.copy(alpha = 0.4f),
                            modifier = Modifier.size(50.dp)
                                .align(Alignment.Center)
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun LoginProfileStep2Preview() {
    AppTheme {
        LoginProfileStep2Content(
            uiState = LoginProfileStep2UIState()
        )
    }
}

private val itemLinearGradient = Brush.linearGradient(
    colors = listOf(
        PinkSecondary,
        Color.White.copy(alpha = 0.4f)
    )
)

private val itemVerticalGradient = Brush.verticalGradient(
    colors = listOf(
        PurplePrimary.copy(alpha = 0.21f),
        Color.Transparent
    )
)