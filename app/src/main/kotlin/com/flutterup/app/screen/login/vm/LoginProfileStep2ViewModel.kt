package com.flutterup.app.screen.login.vm

import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class LoginProfileStep2ViewModel @Inject constructor(
    private val repository: LoginProfileRepository,
    private val navCenter: GlobalNavCenter,
) : BaseRepositoryViewModel(repository) {

    private val _uiState = MutableStateFlow(LoginProfileStep2UIState())
    val uiState: StateFlow<LoginProfileStep2UIState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun addMedia(index: Int, url: String) {
        _uiState.update { uiState ->
            val successStatus = LoginProfileStep2UIState.MediaStatus.Success(url)
            val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = successStatus }
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun startMediaLoading(index: Int) {
        _uiState.update { uiState ->
            val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = LoginProfileStep2UIState.MediaStatus.Loading }
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun endMediaLoading(index: Int) {
        val status = _uiState.value.mediaStatuses.getOrNull(index)

        if (status !is LoginProfileStep2UIState.MediaStatus.Success) {
            _uiState.update { uiState ->
                val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = LoginProfileStep2UIState.MediaStatus.Idle }
                uiState.copy(mediaStatuses = mediaStatuses)
            }
        }
    }
}