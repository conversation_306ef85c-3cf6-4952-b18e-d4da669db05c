package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.Gender
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.login.state.LoginProfileStep1UIState
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class LoginProfileStep1ViewModel @Inject constructor(
    private val repository: LoginProfileRepository,
    private val navCenter: GlobalNavCenter,
) : BaseRepositoryViewModel(repository) {
    private val _uiState = MutableStateFlow(LoginProfileStep1UIState())
    val uiState: StateFlow<LoginProfileStep1UIState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )


    fun updateNickname(nickname: String) {
        _uiState.update { it.copy(nickname = nickname) }
    }

    fun updateGender(gender: Gender) {
        _uiState.update { it.copy(gender = gender) }
    }

    fun updateMeet(gender: Gender) {
        _uiState.update { it.copy(meet = gender) }
    }

    fun updateBirthday(birthday: LocalDate?) {
        _uiState.update { it.copy(birthday = birthday) }
    }

    fun complete() {
        val data = uiState.value

        if (!data.isContinueEnabled) return

        scope.launchWithLoading {
            repository.updateBaseProfile(
                nickname = data.nickname,
                gender = data.gender,
                meet = data.meet,
                birthday = checkNotNull(data.birthday)
            ).collect {
                if (it) { //更新成功
                    navCenter.navigateToLoginProfileStep2()
                }
            }
        }
    }
}