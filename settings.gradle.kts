import java.net.URI

include(":libs:cropper")


pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url = URI.create("https://jitpack.io") }
    }
}

rootProject.name = "FlutterUpApp"
include(":app")
include(":libs:base")
include(":libs:network")
include(":libs:tracking")
include(":libs:billinghelper")
include(":libs:gifts")
include(":libs:players")
include(":libs:alpha_player")
