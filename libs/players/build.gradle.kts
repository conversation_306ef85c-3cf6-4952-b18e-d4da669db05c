plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")


android {
    namespace = "com.flutterup.players"

    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation(project(":libs:base"))
    implementation(project(":libs:network"))

    //alpha_player
    api(project(":libs:alpha_player"))

    implementation(libs.media3.exoplayer)
    implementation(libs.media3.exoplayer.dash)
    implementation(libs.media3.ui)
    implementation(libs.media3.ui.compose)
}