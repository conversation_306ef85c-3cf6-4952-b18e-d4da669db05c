package com.flutterup.cropper

import android.net.Uri
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.canhub.cropper.CropImageOptions
import com.canhub.cropper.CropImageView


@Composable
fun ImageCropper(
    uri: Uri,
    modifier: Modifier = Modifier,
    guidelines: Boolean = false,
    aspectRatioX: Int = 3,
    aspectRatioY: Int = 4,
    cropOptions: CropImageOptions = CropImageOptions(),
    onCropComplete: (CropImageView.CropResult) -> Unit,
) {
    AndroidView(
        factory = { context ->
            CropImageView(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            }
        },
        update = {
            it.guidelines = if (guidelines) CropImageView.Guidelines.ON else CropImageView.Guidelines.OFF
            it.setAspectRatio(aspectRatioX, aspectRatioY)
            it.setImageCropOptions(cropOptions)
            it.setImageUriAsync(uri)

            it.setOnCropImageCompleteListener { view, result ->
                onCropComplete(result)
            }
        },
        modifier = modifier
    )
}