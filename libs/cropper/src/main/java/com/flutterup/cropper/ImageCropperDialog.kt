package com.flutterup.cropper

import android.net.Uri
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.canhub.cropper.CropImageView

/**
 * 图片裁剪对话框
 * 提供完整的图片裁剪功能，包括确认和取消操作
 */
@Composable
fun ImageCropperDialog(
    imageUri: Uri,
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onCropComplete: (Uri?) -> Unit,
    modifier: Modifier = Modifier,
    title: String = "裁剪图片",
    guidelines: Boolean = true,
    aspectRatioX: Int = 1,
    aspectRatioY: Int = 1,
) {
    if (isVisible) {
        var cropperView by remember { mutableStateOf<CropImageView?>(null) }
        var isLoading by remember { mutableStateOf(false) }
        var errorMessage by remember { mutableStateOf<String?>(null) }
        
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Card(
                modifier = modifier
                    .fillMaxWidth(0.95f)
                    .fillMaxHeight(0.8f),
                shape = RoundedCornerShape(16.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 标题栏
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.titleLarge
                        )
                        
                        TextButton(onClick = onDismiss) {
                            Text("取消")
                        }
                    }
                    
                    Divider()
                    
                    // 裁剪器区域
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(16.dp)
                    ) {
                        ImageCropper(
                            uri = imageUri,
                            modifier = Modifier.fillMaxSize(),
                            guidelines = guidelines,
                            aspectRatioX = aspectRatioX,
                            aspectRatioY = aspectRatioY,
                            cropOptions = createDefaultCropOptions(),
                            onCropComplete = { result ->
                                isLoading = false
                                val croppedUri = getCroppedImageUri(result)
                                onCropComplete(croppedUri)
                                onDismiss()
                            },
                            onCropError = { exception ->
                                isLoading = false
                                errorMessage = exception.message
                            }
                        )
                        
                        // 加载指示器
                        if (isLoading) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Card(
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                                    )
                                ) {
                                    Column(
                                        modifier = Modifier.padding(24.dp),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        CircularProgressIndicator()
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Text("正在裁剪...")
                                    }
                                }
                            }
                        }
                    }
                    
                    // 错误信息
                    errorMessage?.let { message ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            )
                        ) {
                            Text(
                                text = message,
                                modifier = Modifier.padding(16.dp),
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    
                    Divider()
                    
                    // 底部按钮
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.End
                    ) {
                        OutlinedButton(
                            onClick = onDismiss,
                            enabled = !isLoading
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Button(
                            onClick = {
                                isLoading = true
                                errorMessage = null
                                // 触发裁剪操作
                                // 注意：实际的裁剪操作需要通过CropImageView的API来触发
                                // 这里只是示例，实际使用时需要获取CropImageView实例并调用相应方法
                            },
                            enabled = !isLoading
                        ) {
                            Text("确认")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 图片裁剪配置
 */
data class CropperConfig(
    val aspectRatioX: Int = 1,
    val aspectRatioY: Int = 1,
    val guidelines: Boolean = true,
    val outputWidth: Int = 1080,
    val outputHeight: Int = 1080,
    val compressQuality: Int = 90
)

/**
 * 带配置的图片裁剪对话框
 */
@Composable
fun ConfigurableImageCropperDialog(
    imageUri: Uri,
    isVisible: Boolean,
    config: CropperConfig = CropperConfig(),
    onDismiss: () -> Unit,
    onCropComplete: (Uri?) -> Unit,
    modifier: Modifier = Modifier,
    title: String = "裁剪图片"
) {
    ImageCropperDialog(
        imageUri = imageUri,
        isVisible = isVisible,
        onDismiss = onDismiss,
        onCropComplete = onCropComplete,
        modifier = modifier,
        title = title,
        guidelines = config.guidelines,
        aspectRatioX = config.aspectRatioX,
        aspectRatioY = config.aspectRatioY
    )
}
