package com.flutterup.cropper

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.canhub.cropper.CropImageView

/**
 * ImageCropper使用示例
 * 
 * 这个示例展示了如何使用ImageCropper组件：
 * 1. 选择图片
 * 2. 裁剪图片
 * 3. 处理裁剪结果
 */
@Composable
fun ImageCropperExample() {
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }
    var croppedImageUri by remember { mutableStateOf<Uri?>(null) }
    var showCropper by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    val context = LocalContext.current
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        if (uri != null) {
            selectedImageUri = uri
            showCropper = true
            errorMessage = null
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 选择图片按钮
        Button(
            onClick = { imagePickerLauncher.launch("image/*") }
        ) {
            Text("选择图片")
        }
        
        // 显示错误信息
        errorMessage?.let { message ->
            Card(
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
            ) {
                Text(
                    text = message,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        // 显示裁剪结果
        croppedImageUri?.let { uri ->
            Card {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text("裁剪完成！")
                    Text("URI: $uri")
                }
            }
        }
        
        // 图片裁剪器
        if (showCropper && selectedImageUri != null) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
            ) {
                ImageCropper(
                    uri = selectedImageUri!!,
                    modifier = Modifier.fillMaxSize(),
                    guidelines = true,
                    aspectRatioX = 3,
                    aspectRatioY = 4,
                    cropOptions = createDefaultCropOptions(),
                    onCropComplete = { result ->
                        val uri = getCroppedImageUri(result)
                        if (uri != null) {
                            croppedImageUri = uri
                            showCropper = false
                            errorMessage = null
                        } else {
                            errorMessage = "裁剪失败：无法获取裁剪后的图片"
                        }
                    },
                    onCropError = { exception ->
                        errorMessage = "裁剪错误：${exception.message}"
                        showCropper = false
                    }
                )
            }
            
            // 控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Button(
                    onClick = { showCropper = false }
                ) {
                    Text("取消")
                }
            }
        }
    }
}

/**
 * 简化版本的ImageCropper使用示例
 * 适用于只需要基本裁剪功能的场景
 */
@Composable
fun SimpleImageCropperExample(
    imageUri: Uri,
    onCropComplete: (Uri?) -> Unit,
    onCancel: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 裁剪器占据大部分空间
        ImageCropper(
            uri = imageUri,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            guidelines = true,
            aspectRatioX = 1,
            aspectRatioY = 1,
            onCropComplete = { result ->
                onCropComplete(getCroppedImageUri(result))
            },
            onCropError = { _ ->
                onCropComplete(null)
            }
        )
        
        // 底部按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            OutlinedButton(onClick = onCancel) {
                Text("取消")
            }
        }
    }
}
