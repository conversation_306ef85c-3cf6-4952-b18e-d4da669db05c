# ImageCropper 图片裁剪组件

基于 Android-Image-Cropper 库的 Compose 封装，提供简单易用的图片裁剪功能。

## 功能特性

- ✅ Compose 原生支持
- ✅ 自定义宽高比
- ✅ 网格线显示
- ✅ 错误处理
- ✅ 资源自动管理
- ✅ 多种使用方式

## 基本使用

### 1. 简单使用

```kotlin
@Composable
fun MyScreen() {
    var imageUri by remember { mutableStateOf<Uri?>(null) }
    
    imageUri?.let { uri ->
        ImageCropper(
            uri = uri,
            modifier = Modifier.fillMaxSize(),
            guidelines = true,
            aspectRatioX = 1,
            aspectRatioY = 1,
            onCropComplete = { result ->
                val croppedUri = getCroppedImageUri(result)
                // 处理裁剪结果
            },
            onCropError = { exception ->
                // 处理错误
            }
        )
    }
}
```

### 2. 使用对话框

```kotlin
@Composable
fun MyScreen() {
    var showCropper by remember { mutableStateOf(false) }
    var imageUri by remember { mutableStateOf<Uri?>(null) }
    
    Button(onClick = { showCropper = true }) {
        Text("裁剪图片")
    }
    
    imageUri?.let { uri ->
        ImageCropperDialog(
            imageUri = uri,
            isVisible = showCropper,
            onDismiss = { showCropper = false },
            onCropComplete = { croppedUri ->
                // 处理裁剪结果
                showCropper = false
            }
        )
    }
}
```

### 3. 自定义配置

```kotlin
val cropConfig = CropperConfig(
    aspectRatioX = 3,
    aspectRatioY = 4,
    guidelines = true,
    outputWidth = 1080,
    outputHeight = 1440,
    compressQuality = 90
)

ConfigurableImageCropperDialog(
    imageUri = imageUri,
    isVisible = showCropper,
    config = cropConfig,
    onDismiss = { showCropper = false },
    onCropComplete = { croppedUri ->
        // 处理结果
    }
)
```

## API 参考

### ImageCropper

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| uri | Uri | - | 要裁剪的图片URI |
| modifier | Modifier | Modifier | 修饰符 |
| guidelines | Boolean | false | 是否显示网格线 |
| aspectRatioX | Int | 3 | 宽高比的宽度值 |
| aspectRatioY | Int | 4 | 宽高比的高度值 |
| cropOptions | CropImageOptions | CropImageOptions() | 裁剪选项 |
| onCropComplete | (CropImageView.CropResult) -> Unit | - | 裁剪完成回调 |
| onCropError | ((Exception) -> Unit)? | null | 错误回调 |

### 辅助函数

#### getCroppedImageUri
```kotlin
fun getCroppedImageUri(cropResult: CropImageView.CropResult): Uri?
```
从裁剪结果中获取裁剪后的图片URI。

#### createDefaultCropOptions
```kotlin
fun createDefaultCropOptions(
    outputCompressFormat: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG,
    outputCompressQuality: Int = 90
): CropImageOptions
```
创建默认的裁剪选项配置。

## 常见用法示例

### 头像裁剪
```kotlin
ImageCropper(
    uri = avatarUri,
    aspectRatioX = 1,
    aspectRatioY = 1,
    guidelines = true,
    cropOptions = createDefaultCropOptions(
        outputCompressQuality = 95
    ),
    onCropComplete = { result ->
        val croppedUri = getCroppedImageUri(result)
        // 上传头像
    }
)
```

### 封面图裁剪
```kotlin
ImageCropper(
    uri = coverUri,
    aspectRatioX = 16,
    aspectRatioY = 9,
    guidelines = false,
    onCropComplete = { result ->
        // 处理封面图
    }
)
```

## 注意事项

1. **权限要求**：确保应用有读取外部存储的权限
2. **内存管理**：大图片可能导致内存问题，建议先压缩
3. **文件存储**：裁剪后的图片会保存在应用的缓存目录
4. **生命周期**：组件会自动处理资源清理

## 依赖

```kotlin
implementation("com.github.CanHub:Android-Image-Cropper:4.3.3")
```

## 更新日志

### v1.0.0
- 初始版本
- 基本裁剪功能
- Compose 封装
- 错误处理
