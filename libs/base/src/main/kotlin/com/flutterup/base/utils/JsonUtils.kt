package com.flutterup.base.utils

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class JsonUtils @Inject constructor(val moshi: <PERSON><PERSON>) {

    /**
     * 将对象序列化为JSON字符串
     */
    fun <T> toJson(obj: T, clazz: Class<T>): String? {
        return try {
            val adapter = moshi.adapter(clazz)
            adapter.toJson(obj)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to serialize object to JSON", e)
            null
        }
    }

    /**
     * 将JSON字符串反序列化为对象
     */
    fun <T> fromJson(json: String, clazz: Class<T>): T? {
        return try {
            val adapter = moshi.adapter(clazz)
            adapter.fromJson(json)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to deserialize JSON to object", e)
            null
        }
    }

    /**
     * 将JSON字符串反序列化为对象
     */
    fun <T> fromJson(json: String, adapter: JsonAdapter<T>): T? {
        return try {
            adapter.fromJson(json)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to deserialize JSON to object", e)
            null
        }
    }

    /**
     * 将对象列表序列化为JSON字符串
     */
    fun <T> toJsonList(list: List<T>, clazz: Class<T>): String? {
        return try {
            val type = Types.newParameterizedType(List::class.java, clazz)
            val adapter: JsonAdapter<List<T>> = moshi.adapter(type)
            adapter.toJson(list)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to serialize list to JSON", e)
            null
        }
    }

    /**
     * 将JSON字符串反序列化为对象列表
     */
    fun <T> fromJsonList(json: String, clazz: Class<T>): List<T>? {
        return try {
            val type = Types.newParameterizedType(List::class.java, clazz)
            val adapter: JsonAdapter<List<T>> = moshi.adapter(type)
            adapter.fromJson(json)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to deserialize JSON to list", e)
            null
        }
    }

    /**
     * 获取指定类型的JsonAdapter
     */
    fun <T> getAdapter(clazz: Class<T>): JsonAdapter<T> {
        return moshi.adapter(clazz)
    }

    inline fun <reified T> getAdapter(): JsonAdapter<T> {
        return moshi.adapter(T::class.java)
    }

    /**
     * 格式化JSON字符串（美化输出）
     */
    fun formatJson(json: String): String? {
        return try {
            val adapter = moshi.adapter(Any::class.java).indent("  ")
            val obj = moshi.adapter(Any::class.java).fromJson(json)
            adapter.toJson(obj)
        } catch (e: Exception) {
            Timber.e("JsonUtils", "Failed to format JSON", e)
            null
        }
    }
}
