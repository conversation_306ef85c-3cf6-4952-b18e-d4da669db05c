package com.flutterup.base.permission

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import com.flutterup.base.utils.Timber
import kotlinx.coroutines.launch

/**
 * Compose 权限请求状态
 */
@Stable
class PermissionState(
    private val permissionManager: PermissionManager,
    private val activity: FragmentActivity
) {
    var permissionResult by mutableStateOf<MultiplePermissionResult?>(null)
        private set

    var isRequesting by mutableStateOf(false)
        private set

    var showRationale by mutableStateOf(false)
        private set

    var rationalePermissions by mutableStateOf<List<String>>(emptyList())
        private set

    private var rationaleConfig: PermissionRequestConfig? = null
    private var rationaleCallback: PermissionCallback? = null

    /**
     * 检查权限是否已授权
     */
    fun hasPermission(permission: String): Boolean {
        return permissionManager.hasPermission(activity, permission)
    }

    /**
     * 检查多个权限是否都已授权
     */
    fun hasPermissions(permissions: List<String>): Boolean {
        return permissionManager.hasPermissions(activity, permissions)
    }

    /**
     * 获取权限状态
     */
    fun getPermissionStatus(permission: String): PermissionStatus {
        return permissionManager.getPermissionStatus(activity, permission)
    }

    /**
     * 请求权限
     */
    fun requestPermissions(
        permissions: List<String>,
        config: PermissionRequestConfig = PermissionRequestConfig(permissions),
        onGranted: (MultiplePermissionResult) -> Unit = {},
        onDenied: (MultiplePermissionResult) -> Unit = {}
    ) {
        if (isRequesting) {
            Timber.w("PermissionState", "Permission request already in progress")
            return
        }

        isRequesting = true

        val callback = object : PermissionCallback {
            override fun onGranted(result: MultiplePermissionResult) {
                permissionResult = result
                isRequesting = false
                onGranted(result)
            }

            override fun onDenied(result: MultiplePermissionResult) {
                permissionResult = result
                isRequesting = false
                onDenied(result)
            }

            override fun onShowRationale(
                permissions: List<String>,
                proceed: () -> Unit,
                cancel: () -> Unit
            ) {
                rationalePermissions = permissions
                rationaleConfig = config
                rationaleCallback = this
                showRationale = true
                // 注意：这里不设置 isRequesting = false，因为用户还需要选择
            }
        }

        permissionManager.requestPermissions(activity, permissions, config, callback)
    }

    /**
     * 继续权限请求（在显示说明后）
     */
    fun proceedWithPermissionRequest() {
        showRationale = false
        // 这里需要重新触发权限请求，但跳过说明步骤
        rationaleConfig?.let { config ->
            rationaleCallback?.let { callback ->
                val newConfig = config.copy(showRationale = false) // 移除说明，避免循环
                permissionManager.requestPermissions(activity, rationalePermissions, newConfig, callback)
            }
        }
    }

    /**
     * 取消权限请求
     */
    fun cancelPermissionRequest() {
        showRationale = false
        isRequesting = false
        rationaleCallback?.onDenied(
            MultiplePermissionResult(
                rationalePermissions.associateWith { permission ->
                    PermissionResult(permission, getPermissionStatus(permission))
                }
            )
        )
    }

    /**
     * 打开应用设置
     */
    fun openAppSettings() {
        permissionManager.openAppSettings(activity)
    }
}

/**
 * 记住权限状态
 */
@Composable
fun rememberPermissionState(
    permissionManager: PermissionManager
): PermissionState {
    val context = LocalContext.current
    val activity = remember(context) {
        context as? FragmentActivity ?: error("PermissionState requires FragmentActivity context")
    }

    return remember(permissionManager, activity) {
        PermissionState(permissionManager, activity)
    }
}