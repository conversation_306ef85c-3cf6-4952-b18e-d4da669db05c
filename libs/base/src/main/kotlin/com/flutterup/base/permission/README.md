# 权限管理系统

这是一个功能完整的 Android 权限管理系统，支持 ActivityResultLauncher、Compose 集成、请求限制、自定义前置操作等功能。

## 特性

- ✅ 支持 ActivityResultLauncher 请求权限
- ✅ 支持 Compose 内调用请求权限
- ✅ 支持设置应用内只请求一次
- ✅ 支持设置应用内每天只请求一次
- ✅ 提供 API 给外部获取权限状态
- ✅ 支持设置请求权限前进行自定义操作
- ✅ 支持权限说明对话框
- ✅ 支持永久拒绝检测
- ✅ 支持打开应用设置页面
- ✅ 使用 Hilt 依赖注入
- ✅ 使用 MMKV 存储请求记录

## 快速开始

### 1. 依赖注入

在需要使用权限管理的地方注入 `PermissionManager`：

```kotlin
@AndroidEntryPoint
class MainActivity : FragmentActivity() {
    
    @Inject
    lateinit var permissionManager: PermissionManager
    
    // ...
}
```

### 2. 基本使用

#### 检查权限状态

```kotlin
// 检查单个权限
val hasCamera = permissionManager.hasPermission(this, Manifest.permission.CAMERA)

// 检查多个权限
val permissions = listOf(Manifest.permission.CAMERA, Manifest.permission.ACCESS_FINE_LOCATION)
val hasAllPermissions = permissionManager.hasPermissions(this, permissions)

// 获取权限详细状态
val cameraStatus = permissionManager.getPermissionStatus(this, Manifest.permission.CAMERA)
```

#### 请求单个权限

```kotlin
permissionManager.requestPermission(
    activity = this,
    permission = Manifest.permission.CAMERA,
    config = PermissionRequestConfig(
        permissions = listOf(Manifest.permission.CAMERA),
        limitType = PermissionLimitType.ONCE_PER_DAY,
        rationale = "需要相机权限来拍照"
    ),
    callback = object : PermissionCallback {
        override fun onGranted(result: MultiplePermissionResult) {
            // 权限已授权
        }
        
        override fun onDenied(result: MultiplePermissionResult) {
            // 权限被拒绝
        }
        
        override fun onShowRationale(
            permissions: List<String>,
            proceed: () -> Unit,
            cancel: () -> Unit
        ) {
            // 显示权限说明
        }
    }
)
```

#### 请求多个权限

```kotlin
val permissions = listOf(
    Manifest.permission.CAMERA,
    Manifest.permission.ACCESS_FINE_LOCATION,
    Manifest.permission.RECORD_AUDIO
)

permissionManager.requestPermissions(
    activity = this,
    permissions = permissions,
    config = PermissionRequestConfig(
        permissions = permissions,
        limitType = PermissionLimitType.ONCE_PER_APP,
        rationale = "需要这些权限来提供完整功能"
    ),
    callback = object : PermissionCallback {
        override fun onGranted(result: MultiplePermissionResult) {
            // 所有权限都已授权
        }
        
        override fun onDenied(result: MultiplePermissionResult) {
            // 处理部分或全部权限被拒绝
            val grantedPermissions = result.grantedPermissions
            val deniedPermissions = result.deniedPermissions
            val permanentlyDeniedPermissions = result.permanentlyDeniedPermissions
        }
        
        override fun onShowRationale(permissions: List<String>, proceed: () -> Unit, cancel: () -> Unit) {
            // 显示权限说明对话框
        }
    }
)
```

### 3. Compose 中使用

```kotlin
@Composable
fun MyScreen(permissionManager: PermissionManager) {
    val permissionState = rememberPermissionState(permissionManager)
    
    Column {
        Button(
            onClick = {
                permissionState.requestPermissions(
                    permissions = listOf(Manifest.permission.CAMERA),
                    config = PermissionRequestConfig(
                        permissions = listOf(Manifest.permission.CAMERA),
                        limitType = PermissionLimitType.ONCE_PER_DAY
                    ),
                    onGranted = { result ->
                        // 权限已授权
                    },
                    onDenied = { result ->
                        // 权限被拒绝
                    }
                )
            }
        ) {
            Text("请求相机权限")
        }
        
        // 显示权限状态
        val hasCamera = permissionState.hasPermission(Manifest.permission.CAMERA)
        Text("相机权限: ${if (hasCamera) "已授权" else "未授权"}")
        
        // 处理权限说明对话框
        if (permissionState.showRationale) {
            AlertDialog(
                onDismissRequest = { permissionState.cancelPermissionRequest() },
                title = { Text("权限说明") },
                text = { Text("需要相机权限来拍照") },
                confirmButton = {
                    TextButton(onClick = { permissionState.proceedWithPermissionRequest() }) {
                        Text("继续")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { permissionState.cancelPermissionRequest() }) {
                        Text("取消")
                    }
                }
            )
        }
    }
}
```

### 4. 高级功能

#### 请求限制

```kotlin
val config = PermissionRequestConfig(
    permissions = listOf(Manifest.permission.CAMERA),
    limitType = PermissionLimitType.ONCE_PER_DAY, // 每天只请求一次
    // 或者
    // limitType = PermissionLimitType.ONCE_PER_APP, // 应用内只请求一次
)
```

#### 自定义前置操作

```kotlin
val config = PermissionRequestConfig(
    permissions = listOf(Manifest.permission.CAMERA),
    preAction = object : PermissionPreAction {
        override suspend fun execute(permissions: List<String>, onComplete: (Boolean) -> Unit) {
            // 执行一些前置操作，比如显示引导页面
            showPermissionGuide()
            delay(2000)
            onComplete(true) // 继续请求权限
        }
    }
)
```

#### 处理永久拒绝

```kotlin
override fun onDenied(result: MultiplePermissionResult) {
    if (result.permanentlyDeniedPermissions.isNotEmpty()) {
        // 有权限被永久拒绝，引导用户到设置页面
        showDialog(
            title = "权限被拒绝",
            message = "请在设置中手动开启权限",
            positiveButton = "去设置" to {
                permissionManager.openAppSettings(this@MainActivity)
            }
        )
    }
}
```

## 权限状态

- `GRANTED`: 已授权
- `DENIED`: 被拒绝（可以再次请求）
- `PERMANENTLY_DENIED`: 被永久拒绝（需要到设置页面手动开启）
- `UNKNOWN`: 未知状态

## 请求限制类型

- `NONE`: 无限制
- `ONCE_PER_APP`: 应用内只请求一次
- `ONCE_PER_DAY`: 每天只请求一次

## 注意事项

1. 确保在 `FragmentActivity` 或其子类中使用
2. 权限请求记录存储在 MMKV 中，应用卸载后会清除
3. 每日限制基于日期比较，不会自动清理过期记录
4. 建议在权限被永久拒绝时引导用户到设置页面
5. 自定义前置操作应该是轻量级的，避免长时间阻塞
